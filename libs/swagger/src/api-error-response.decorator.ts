import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiProperty, ApiResponse, ApiResponseExamples, ApiResponseOptions } from '@nestjs/swagger';

export class ApiErrorResponseDto {
  @ApiProperty()
  error: true;

  @ApiProperty({ enum: HttpStatus })
  status: HttpStatus;

  @ApiProperty()
  message: string;

  @ApiProperty()
  cause: string;
}

export function ApiErrorResponse(
  status: HttpStatus,
  message: string,
  cause?: string,
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status,
      example: {
        error: true,
        status,
        message,
        cause,
      },
      type: ApiErrorResponseDto,
    }),
  );
}

export function ApiErrors(
  status: HttpStatus,
  errors: { message: string; cause?: string }[],
  options?: ApiResponseOptions,
) {
  return applyDecorators(
    ApiResponse({
      ...options,
      status,
      type: ApiErrorResponseDto,
      examples: errors.reduce(
        (list, { message, cause }) => {
          list[message] = {
            value: {
              error: true,
              status,
              message,
              cause,
            },
            summary: message,
          };
          return list;
        },
        {} as Record<string, ApiResponseExamples>,
      ),
    }),
  );
}
