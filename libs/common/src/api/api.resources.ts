import { HttpException, HttpStatus } from '@nestjs/common';

export type ApiResponsePayload<T = undefined> = {
  success: true;
  status: HttpStatus;
  data: T;
  message: string;
};

export type ApiErrorPayload = {
  success: false;
  status: HttpStatus;
  error: string;
  message: string;
  details?: unknown;
  timestamp?: string;
};

export const formatHttpException = (error: HttpException, cause = 'ERROR'): ApiErrorPayload => {
  return {
    success: false,
    status: error.getStatus(),
    error: (error.cause as string) || cause,
    message: error.message,
  };
};

export type HttpResponseOptions<T = undefined> = {
  status?: HttpStatus; // TODO: make required
  message?: string;
  data?: T;
};

export class HttpResponse<T = undefined> {
  constructor(options: HttpResponseOptions<T>) {
    const { status = HttpStatus.OK, message = 'Ok', data } = options;
    const response: ApiResponsePayload<T> = {
      success: true,
      status,
      message,
      data: data as T,
    };
    return response;
  }
}
