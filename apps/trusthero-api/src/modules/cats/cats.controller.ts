import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ZodSerializerDto } from 'nestjs-zod';
import { CatDto } from '@models/cats';
import { CatsService } from './cats.service';

@Controller('cats')
export class CatsController {
  constructor(private readonly catsService: CatsService) {}

  @Post()
  // TODO: add swagger
  // TODO: response sanitization
  createCat(@Body() dto: CatDto) {
    return this.catsService.create(dto);
  }

  @Delete(':id')
  deleteCat(@Param('id') catId: string) {
    return this.catsService.delete(catId);
  }

  @Get(':id')
  @ZodSerializerDto(CatDto)
  async getCat(@Param('id') catId: string) {
    const cat = await this.catsService.findOne(catId);
    console.log('cat.id', cat.id);
    return cat;
  }

  @Get()
  getAllCats() {
    return this.catsService.findAll();
  }
}
