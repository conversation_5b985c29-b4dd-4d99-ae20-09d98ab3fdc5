import { <PERSON>p, Schema, SchemaFactory, Virtual } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { registerVirtualId } from '@libs/common/database/mongo';
import { CatModel } from '@models/cats';

@Schema({ collection: 'cats' })
export class Cat implements CatModel {
  @Virtual({
    get: function () {
      return String(this._id);
    },
  })
  id: string;

  @Prop()
  name: string;

  @Prop()
  age: number;

  @Prop()
  breed: string;
}

export type CatDocument = HydratedDocument<Cat>;

export const CatEntitySchema = SchemaFactory.createForClass(Cat);
