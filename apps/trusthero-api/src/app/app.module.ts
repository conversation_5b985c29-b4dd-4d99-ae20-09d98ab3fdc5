import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ZodSerializerInterceptor, ZodValidationPipe } from 'nestjs-zod';
import { ConfigModule, ConfigService } from '@libs/common/config';
import { LoggerModule, usePinoHttpOptions } from '@libs/pino-logger';
import { CatsModule } from '../modules/cats/cats.module';
import { EnvironmentVariables, EnvVariablesSchema } from './app.config';
import { AppController } from './app.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      schema: EnvVariablesSchema,
    }),

    LoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => {
        const pinoHttp = usePinoHttpOptions({ configService: config });
        return { pinoHttp };
      },
    }),

    CatsModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
  ],
})
export class AppModule {}
