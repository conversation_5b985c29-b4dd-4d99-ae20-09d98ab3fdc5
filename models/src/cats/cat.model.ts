import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CatSchema = z.object({
  id: z.string().describe('Cat id'),
  name: z.string().min(2, 'Name must be at least 2 characters').describe('Cat name'),
  age: z.number().positive().describe('Cat age'),
  breed: z.string().describe('Cat breed'),
});

export type CatModel = z.infer<typeof CatSchema>;

export class CatDto extends createZodDto(CatSchema) {}
